2025-05-21 17:46:21.423 [main] INFO  com.ticketbooking.Main - Starting Ticket Booking System
2025-05-21 17:46:23.903 [main] INFO  com.ticketbooking.Main - Set Nimbus look and feel with custom colors
2025-05-21 17:46:23.985 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load application icon
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:55) ~[classes/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[classes/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-21 17:46:24.023 [AWT-EventQueue-0] WARN  com.ticketbooking.view.LoginPanel - Could not load logo image
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.LoginPanel.initializeComponents(LoginPanel.java:78) ~[classes/:?]
	at com.ticketbooking.view.LoginPanel.<init>(LoginPanel.java:57) ~[classes/:?]
	at com.ticketbooking.view.MainFrame.initializePanels(MainFrame.java:105) ~[classes/:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:69) ~[classes/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[classes/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-21 17:46:27.500 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Login panel initialized
2025-05-21 17:46:27.787 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-21 17:46:28.257 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:28.791 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-21 17:46:28.792 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Home panel initialized
2025-05-21 17:46:29.003 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Booking panel initialized
2025-05-21 17:46:29.646 [AWT-EventQueue-0] WARN  com.ticketbooking.view.AdminPanel - Could not load tab icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.AdminPanel.initializeComponents(AdminPanel.java:162) ~[classes/:?]
	at com.ticketbooking.view.AdminPanel.<init>(AdminPanel.java:67) ~[classes/:?]
	at com.ticketbooking.view.MainFrame.initializePanels(MainFrame.java:108) ~[classes/:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:69) ~[classes/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[classes/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-21 17:46:29.662 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-21 17:46:29.679 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:29.690 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-21 17:46:29.691 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-21 17:46:29.708 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:29.753 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:29.781 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:29.807 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:29.814 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Booking list refreshed
2025-05-21 17:46:29.815 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-21 17:46:29.831 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:29.851 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - User list refreshed
2025-05-21 17:46:29.852 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-21 17:46:29.867 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:29.874 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-21 17:46:29.914 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:29.943 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:29.972 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:29.996 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:30.002 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-21 17:46:30.029 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:30.041 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Admin panel initialized
2025-05-21 17:46:30.079 [AWT-EventQueue-0] INFO  com.ticketbooking.view.RegistrationPanel - Registration panel initialized
2025-05-21 17:46:30.392 [AWT-EventQueue-0] INFO  com.ticketbooking.view.MainFrame - Main frame initialized
2025-05-21 17:46:34.300 [AWT-EventQueue-0] INFO  com.ticketbooking.Main - Application started successfully
2025-05-21 17:46:59.352 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Guest login
2025-05-21 17:46:59.364 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load menu icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.updateMenuBar(MainFrame.java:161) ~[classes/:?]
	at com.ticketbooking.view.MainFrame.setCurrentUser(MainFrame.java:135) ~[classes/:?]
	at com.ticketbooking.view.LoginPanel.handleGuestLogin(LoginPanel.java:438) ~[classes/:?]
	at com.ticketbooking.view.LoginPanel.lambda$createLoginPanel$4(LoginPanel.java:258) ~[classes/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-21 17:46:59.396 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-21 17:46:59.415 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:46:59.440 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-21 17:47:14.471 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting event with ID: 1
2025-05-21 17:47:14.486 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:47:14.492 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Loaded event details for event ID: 1
2025-05-21 17:47:22.907 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-21 17:47:22.924 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:47:22.947 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-21 17:47:24.835 [AWT-EventQueue-0] INFO  com.ticketbooking.view.MainFrame - Application closing
2025-05-21 17:47:24.835 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection closed
2025-05-21 17:48:08.111 [main] INFO  com.ticketbooking.Main - Starting Ticket Booking System
2025-05-21 17:48:09.629 [main] INFO  com.ticketbooking.Main - Set Nimbus look and feel with custom colors
2025-05-21 17:48:09.725 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load application icon
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:55) ~[classes/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[classes/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-21 17:48:09.775 [AWT-EventQueue-0] WARN  com.ticketbooking.view.LoginPanel - Could not load logo image
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.LoginPanel.initializeComponents(LoginPanel.java:78) ~[classes/:?]
	at com.ticketbooking.view.LoginPanel.<init>(LoginPanel.java:57) ~[classes/:?]
	at com.ticketbooking.view.MainFrame.initializePanels(MainFrame.java:105) ~[classes/:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:69) ~[classes/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[classes/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-21 17:48:12.743 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Login panel initialized
2025-05-21 17:48:13.024 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-21 17:48:13.397 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:13.673 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-21 17:48:13.674 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Home panel initialized
2025-05-21 17:48:13.936 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Booking panel initialized
2025-05-21 17:48:14.398 [AWT-EventQueue-0] WARN  com.ticketbooking.view.AdminPanel - Could not load tab icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.AdminPanel.initializeComponents(AdminPanel.java:162) ~[classes/:?]
	at com.ticketbooking.view.AdminPanel.<init>(AdminPanel.java:67) ~[classes/:?]
	at com.ticketbooking.view.MainFrame.initializePanels(MainFrame.java:108) ~[classes/:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:69) ~[classes/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[classes/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-21 17:48:14.401 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-21 17:48:14.416 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.424 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-21 17:48:14.425 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-21 17:48:14.440 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.475 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.497 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.518 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.524 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Booking list refreshed
2025-05-21 17:48:14.524 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-21 17:48:14.539 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.554 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - User list refreshed
2025-05-21 17:48:14.554 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-21 17:48:14.568 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.575 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-21 17:48:14.591 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.617 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.639 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.661 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.668 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-21 17:48:14.687 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-21 17:48:14.694 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Admin panel initialized
2025-05-21 17:48:14.718 [AWT-EventQueue-0] INFO  com.ticketbooking.view.RegistrationPanel - Registration panel initialized
2025-05-21 17:48:14.899 [AWT-EventQueue-0] INFO  com.ticketbooking.view.MainFrame - Main frame initialized
2025-05-21 17:48:18.218 [AWT-EventQueue-0] INFO  com.ticketbooking.Main - Application started successfully
