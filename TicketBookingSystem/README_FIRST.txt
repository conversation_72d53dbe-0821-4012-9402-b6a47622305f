====================================
   TICKET BOOKING SYSTEM README
====================================

Thank you for using the Ticket Booking System!

This file contains instructions on how to run the application.

PREREQUISITES:
-------------
- Java Development Kit (JDK) 17 or higher
- PostgreSQL 12 or higher (installed and running)

RUNNING THE APPLICATION:
----------------------
There are several ways to run the application:

1. Double-click the "TicketBookingSystem.command" file
   This will open a terminal window and run the application.

2. From the terminal:
   a. Open a terminal
   b. Navigate to the TicketBookingSystem directory
   c. Run the command: ./start.sh

LOGIN INFORMATION:
----------------
- Admin Login: 
  Username: admin
  Password: admin123

- Guest Access: 
  Click "Continue as Guest" to browse events without logging in

- User Registration: 
  Click "Register" to create a new user account

TROUBLESHOOTING:
--------------
If you encounter any issues:

1. Make sure PostgreSQL is running
2. Check that the database is properly set up
3. Ensure Java is installed and properly configured

For more detailed instructions, please refer to the RUNNING.md file.

====================================
       ENJOY THE APPLICATION!
====================================
