2025-05-20 01:33:01.329 [main] INFO  com.ticketbooking.Main - Starting Ticket Booking System
2025-05-20 01:33:01.874 [main] INFO  com.ticketbooking.Main - Set Nimbus look and feel with custom colors
2025-05-20 01:33:01.961 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load application icon
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:55) ~[build/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[build/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 01:33:01.982 [AWT-EventQueue-0] WARN  com.ticketbooking.view.LoginPanel - Could not load logo image
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.LoginPanel.initializeComponents(LoginPanel.java:78) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.<init>(LoginPanel.java:57) ~[build/:?]
	at com.ticketbooking.view.MainFrame.initializePanels(MainFrame.java:105) ~[build/:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:69) ~[build/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[build/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 01:33:03.656 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Login panel initialized
2025-05-20 01:33:03.713 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 01:33:04.009 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:33:04.129 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 01:33:04.129 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Home panel initialized
2025-05-20 01:33:04.239 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Booking panel initialized
2025-05-20 01:33:04.418 [AWT-EventQueue-0] WARN  com.ticketbooking.view.AdminPanel - Could not load tab icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.AdminPanel.initializeComponents(AdminPanel.java:162) ~[build/:?]
	at com.ticketbooking.view.AdminPanel.<init>(AdminPanel.java:67) ~[build/:?]
	at com.ticketbooking.view.MainFrame.initializePanels(MainFrame.java:108) ~[build/:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:69) ~[build/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[build/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 01:33:04.419 [AWT-EventQueue-0] ERROR com.ticketbooking.Main - Error starting application
java.lang.NullPointerException: Cannot invoke "com.ticketbooking.model.User.getUsername()" because the return value of "com.ticketbooking.view.MainFrame.getCurrentUser()" is null
	at com.ticketbooking.view.AdminPanel.initializeComponents(AdminPanel.java:195) ~[build/:?]
	at com.ticketbooking.view.AdminPanel.<init>(AdminPanel.java:67) ~[build/:?]
	at com.ticketbooking.view.MainFrame.initializePanels(MainFrame.java:108) ~[build/:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:69) ~[build/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[build/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 01:40:03.969 [main] INFO  com.ticketbooking.Main - Starting Ticket Booking System
2025-05-20 01:40:04.721 [main] INFO  com.ticketbooking.Main - Set Nimbus look and feel with custom colors
2025-05-20 01:40:04.767 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load application icon
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:55) ~[build/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[build/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 01:40:04.792 [AWT-EventQueue-0] WARN  com.ticketbooking.view.LoginPanel - Could not load logo image
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.LoginPanel.initializeComponents(LoginPanel.java:78) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.<init>(LoginPanel.java:57) ~[build/:?]
	at com.ticketbooking.view.MainFrame.initializePanels(MainFrame.java:105) ~[build/:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:69) ~[build/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[build/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 01:40:05.824 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Login panel initialized
2025-05-20 01:40:05.878 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 01:40:06.146 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:06.267 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 01:40:06.267 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Home panel initialized
2025-05-20 01:40:06.422 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Booking panel initialized
2025-05-20 01:40:06.627 [AWT-EventQueue-0] WARN  com.ticketbooking.view.AdminPanel - Could not load tab icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.AdminPanel.initializeComponents(AdminPanel.java:162) ~[build/:?]
	at com.ticketbooking.view.AdminPanel.<init>(AdminPanel.java:67) ~[build/:?]
	at com.ticketbooking.view.MainFrame.initializePanels(MainFrame.java:108) ~[build/:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:69) ~[build/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[build/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 01:40:06.630 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 01:40:06.642 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:06.647 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 01:40:06.647 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 01:40:06.717 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:06.748 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:06.752 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Booking list refreshed
2025-05-20 01:40:06.752 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 01:40:06.762 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:06.772 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - User list refreshed
2025-05-20 01:40:06.772 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 01:40:06.786 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:06.791 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 01:40:06.802 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:06.815 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:06.819 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 01:40:06.830 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:06.834 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Admin panel initialized
2025-05-20 01:40:06.847 [AWT-EventQueue-0] INFO  com.ticketbooking.view.RegistrationPanel - Registration panel initialized
2025-05-20 01:40:06.879 [AWT-EventQueue-0] INFO  com.ticketbooking.view.MainFrame - Main frame initialized
2025-05-20 01:40:08.211 [AWT-EventQueue-0] INFO  com.ticketbooking.Main - Application started successfully
2025-05-20 01:40:26.622 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Authenticating user: admin
2025-05-20 01:40:26.633 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:26.641 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error updating last login for user ID: 1
org.postgresql.util.PSQLException: ERROR: column "last_login_date" of relation "users" does not exist
  Position: 18
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.updateLastLogin(UserDAO.java:389) ~[build/:?]
	at com.ticketbooking.dao.UserDAO.authenticateUser(UserDAO.java:141) ~[build/:?]
	at com.ticketbooking.controller.UserController.authenticateUser(UserController.java:78) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.handleAdminLogin(LoginPanel.java:411) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.lambda$createLoginPanel$1(LoginPanel.java:242) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 01:40:26.643 [AWT-EventQueue-0] INFO  com.ticketbooking.dao.UserDAO - User authenticated successfully: admin
2025-05-20 01:40:26.644 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Admin logged in: admin
2025-05-20 01:40:26.648 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load menu icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.updateMenuBar(MainFrame.java:161) ~[build/:?]
	at com.ticketbooking.view.MainFrame.setCurrentUser(MainFrame.java:135) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.handleAdminLogin(LoginPanel.java:415) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.lambda$createLoginPanel$1(LoginPanel.java:242) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 01:40:26.669 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 01:40:26.679 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:26.683 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 01:40:26.684 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 01:40:26.693 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:26.708 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:26.711 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Booking list refreshed
2025-05-20 01:40:26.712 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 01:40:26.721 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:26.725 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - User list refreshed
2025-05-20 01:40:26.725 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 01:40:26.734 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:26.738 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 01:40:26.747 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:26.762 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 01:40:26.765 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 01:40:26.774 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:06.907 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Creating new user: user1
2025-05-20 11:15:06.919 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:06.931 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:06.933 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error adding user: user1
org.postgresql.util.PSQLException: ERROR: column "full_name" of relation "users" does not exist
  Position: 53
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.addUser(UserDAO.java:177) ~[build/:?]
	at com.ticketbooking.controller.UserController.createUser(UserController.java:121) ~[build/:?]
	at com.ticketbooking.view.AdminPanel.lambda$handleAddUser$19(AdminPanel.java:1542) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) [?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) [?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) [?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) ~[?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) ~[?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) ~[?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:117) ~[?:?]
	at java.awt.WaitDispatchSupport$2.run(WaitDispatchSupport.java:191) ~[?:?]
	at java.awt.WaitDispatchSupport$4.run(WaitDispatchSupport.java:236) ~[?:?]
	at java.awt.WaitDispatchSupport$4.run(WaitDispatchSupport.java:234) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:319) ~[?:?]
	at java.awt.WaitDispatchSupport.enter(WaitDispatchSupport.java:234) ~[?:?]
	at java.awt.Dialog.show(Dialog.java:1079) ~[?:?]
	at java.awt.Component.show(Component.java:1728) ~[?:?]
	at java.awt.Component.setVisible(Component.java:1675) ~[?:?]
	at java.awt.Window.setVisible(Window.java:1036) ~[?:?]
	at java.awt.Dialog.setVisible(Dialog.java:1015) ~[?:?]
	at com.ticketbooking.view.AdminPanel.handleAddUser(AdminPanel.java:1571) ~[build/:?]
	at com.ticketbooking.view.AdminPanel.lambda$createUsersTab$7(AdminPanel.java:462) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 11:15:27.626 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Creating new user: user0
2025-05-20 11:15:27.638 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:27.650 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:27.651 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error adding user: user0
org.postgresql.util.PSQLException: ERROR: column "full_name" of relation "users" does not exist
  Position: 53
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.addUser(UserDAO.java:177) ~[build/:?]
	at com.ticketbooking.controller.UserController.createUser(UserController.java:121) ~[build/:?]
	at com.ticketbooking.view.AdminPanel.lambda$handleAddUser$19(AdminPanel.java:1542) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) [?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) [?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) [?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) ~[?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) ~[?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) ~[?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:117) ~[?:?]
	at java.awt.WaitDispatchSupport$2.run(WaitDispatchSupport.java:191) ~[?:?]
	at java.awt.WaitDispatchSupport$4.run(WaitDispatchSupport.java:236) ~[?:?]
	at java.awt.WaitDispatchSupport$4.run(WaitDispatchSupport.java:234) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:319) ~[?:?]
	at java.awt.WaitDispatchSupport.enter(WaitDispatchSupport.java:234) ~[?:?]
	at java.awt.Dialog.show(Dialog.java:1079) ~[?:?]
	at java.awt.Component.show(Component.java:1728) ~[?:?]
	at java.awt.Component.setVisible(Component.java:1675) ~[?:?]
	at java.awt.Window.setVisible(Window.java:1036) ~[?:?]
	at java.awt.Dialog.setVisible(Dialog.java:1015) ~[?:?]
	at com.ticketbooking.view.AdminPanel.handleAddUser(AdminPanel.java:1571) ~[build/:?]
	at com.ticketbooking.view.AdminPanel.lambda$createUsersTab$7(AdminPanel.java:462) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 11:15:37.706 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:15:37.715 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:37.720 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 11:15:37.720 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 11:15:37.728 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:37.742 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:37.745 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Booking list refreshed
2025-05-20 11:15:37.745 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 11:15:37.753 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:37.757 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - User list refreshed
2025-05-20 11:15:37.757 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:15:37.765 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:37.768 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 11:15:37.776 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:37.788 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:37.791 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 11:15:37.799 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:40.238 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:15:40.246 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:40.255 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 11:15:49.095 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting event with ID: 1
2025-05-20 11:15:49.105 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:49.109 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Loaded event details for event ID: 1
2025-05-20 11:15:55.062 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:15:55.071 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:55.079 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 11:15:57.846 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting event with ID: 2
2025-05-20 11:15:57.854 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:15:57.858 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Loaded event details for event ID: 2
2025-05-20 11:16:12.865 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:16:12.876 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:16:12.884 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 11:17:09.234 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:17:09.243 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:17:09.247 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 11:17:09.247 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 11:17:09.256 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:17:09.268 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:17:09.271 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Booking list refreshed
2025-05-20 11:17:09.271 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 11:17:09.280 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:17:09.283 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - User list refreshed
2025-05-20 11:17:09.283 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:17:09.291 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:17:09.295 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 11:17:09.302 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:17:09.315 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:17:09.318 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 11:17:09.326 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:19:20.410 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Creating new event: Hari Raya Eid afitri
2025-05-20 11:19:20.419 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:19:20.427 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event created: Hari Raya Eid afitri
2025-05-20 11:19:20.429 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:19:20.440 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:19:20.444 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 11:19:30.095 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Deleting event with ID: 4
2025-05-20 11:19:30.104 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:19:30.111 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event deleted: Hari Raya Eid afitri
2025-05-20 11:19:30.111 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:19:30.119 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:19:30.126 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 11:19:30.126 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 11:19:30.135 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:19:30.146 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:19:30.149 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Booking list refreshed
2025-05-20 11:19:35.694 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting event with ID: 3
2025-05-20 11:19:35.703 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:20:04.515 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Updating event with ID: 3
2025-05-20 11:20:04.524 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:20:04.536 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:20:04.542 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event updated: Tech Conference
2025-05-20 11:20:04.545 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:20:04.554 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:20:04.558 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 11:20:07.799 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:20:07.808 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:20:07.812 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 11:20:08.449 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:20:08.458 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:20:08.461 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 11:21:25.171 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load menu icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.updateMenuBar(MainFrame.java:161) ~[build/:?]
	at com.ticketbooking.view.MainFrame.lambda$updateMenuBar$3(MainFrame.java:222) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.AbstractButton.doClick(AbstractButton.java:374) ~[?:?]
	at javax.swing.plaf.basic.BasicMenuItemUI.doClick(BasicMenuItemUI.java:1029) ~[?:?]
	at javax.swing.plaf.basic.BasicMenuItemUI$Handler.mouseReleased(BasicMenuItemUI.java:1073) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 11:21:30.961 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Guest login
2025-05-20 11:21:30.961 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load menu icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.updateMenuBar(MainFrame.java:161) ~[build/:?]
	at com.ticketbooking.view.MainFrame.setCurrentUser(MainFrame.java:135) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.handleGuestLogin(LoginPanel.java:438) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.lambda$createLoginPanel$4(LoginPanel.java:258) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 11:21:30.965 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 11:21:30.977 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:21:30.985 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 11:23:41.349 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: Abubakar
2025-05-20 11:23:41.359 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:23:57.466 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if email exists: <EMAIL>
2025-05-20 11:23:57.476 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:22.302 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: Abubakar
2025-05-20 11:24:22.312 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:22.315 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if email exists: <EMAIL>
2025-05-20 11:24:22.323 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:22.326 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Registering new user: Abubakar
2025-05-20 11:24:22.333 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:22.344 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:22.345 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error adding user: Abubakar
org.postgresql.util.PSQLException: ERROR: column "full_name" of relation "users" does not exist
  Position: 53
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.addUser(UserDAO.java:177) ~[build/:?]
	at com.ticketbooking.controller.UserController.registerUser(UserController.java:168) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel.handleRegistration(RegistrationPanel.java:453) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel$3.actionPerformed(RegistrationPanel.java:135) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 11:24:22.346 [AWT-EventQueue-0] ERROR com.ticketbooking.view.RegistrationPanel - Registration failed for username: Abubakar
2025-05-20 11:24:35.988 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: Abubakar
2025-05-20 11:24:35.997 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:36.000 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if email exists: <EMAIL>
2025-05-20 11:24:36.009 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:36.012 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Registering new user: Abubakar
2025-05-20 11:24:36.020 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:36.030 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:36.032 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error adding user: Abubakar
org.postgresql.util.PSQLException: ERROR: column "full_name" of relation "users" does not exist
  Position: 53
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.addUser(UserDAO.java:177) ~[build/:?]
	at com.ticketbooking.controller.UserController.registerUser(UserController.java:168) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel.handleRegistration(RegistrationPanel.java:453) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel$3.actionPerformed(RegistrationPanel.java:135) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 11:24:36.033 [AWT-EventQueue-0] ERROR com.ticketbooking.view.RegistrationPanel - Registration failed for username: Abubakar
2025-05-20 11:24:44.339 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: Abubakar
2025-05-20 11:24:44.348 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:44.351 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if email exists: <EMAIL>
2025-05-20 11:24:44.359 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:44.362 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Registering new user: Abubakar
2025-05-20 11:24:44.370 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:44.380 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:44.382 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error adding user: Abubakar
org.postgresql.util.PSQLException: ERROR: column "full_name" of relation "users" does not exist
  Position: 53
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.addUser(UserDAO.java:177) ~[build/:?]
	at com.ticketbooking.controller.UserController.registerUser(UserController.java:168) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel.handleRegistration(RegistrationPanel.java:453) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel$3.actionPerformed(RegistrationPanel.java:135) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 11:24:44.383 [AWT-EventQueue-0] ERROR com.ticketbooking.view.RegistrationPanel - Registration failed for username: Abubakar
2025-05-20 11:24:47.023 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: Abubakar
2025-05-20 11:24:47.032 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:47.035 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if email exists: <EMAIL>
2025-05-20 11:24:47.044 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:47.047 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Registering new user: Abubakar
2025-05-20 11:24:47.054 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:47.066 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 11:24:47.067 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error adding user: Abubakar
org.postgresql.util.PSQLException: ERROR: column "full_name" of relation "users" does not exist
  Position: 53
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.addUser(UserDAO.java:177) ~[build/:?]
	at com.ticketbooking.controller.UserController.registerUser(UserController.java:168) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel.handleRegistration(RegistrationPanel.java:453) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel$3.actionPerformed(RegistrationPanel.java:135) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 11:24:47.068 [AWT-EventQueue-0] ERROR com.ticketbooking.view.RegistrationPanel - Registration failed for username: Abubakar
2025-05-20 15:31:17.892 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Guest login
2025-05-20 15:31:17.893 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load menu icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.updateMenuBar(MainFrame.java:161) ~[build/:?]
	at com.ticketbooking.view.MainFrame.setCurrentUser(MainFrame.java:135) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.handleGuestLogin(LoginPanel.java:438) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.lambda$createLoginPanel$4(LoginPanel.java:258) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 15:31:17.933 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 15:31:17.950 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 15:31:17.967 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 15:31:25.875 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting event with ID: 1
2025-05-20 15:31:25.883 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 15:31:25.889 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Loaded event details for event ID: 1
2025-05-20 15:31:47.561 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 15:31:47.572 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 15:31:47.579 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 17:44:27.236 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting event with ID: 2
2025-05-20 17:44:27.290 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 17:44:27.306 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Loaded event details for event ID: 2
2025-05-20 17:45:02.373 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Creating new booking for event ID: 2 by customer: ABDOUALYE
2025-05-20 17:45:02.374 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting event with ID: 2
2025-05-20 17:45:02.417 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 17:45:02.440 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 17:45:02.441 [AWT-EventQueue-0] INFO  com.ticketbooking.dao.BookingDAO - Starting transaction for new booking by: ABDOUALYE
2025-05-20 17:45:02.469 [AWT-EventQueue-0] INFO  com.ticketbooking.dao.BookingDAO - Updated available seats for event ID: 2, reduced by: 1
2025-05-20 17:45:02.486 [AWT-EventQueue-0] INFO  com.ticketbooking.dao.BookingDAO - Booking transaction committed successfully. Booking ID: 3
2025-05-20 17:45:02.486 [AWT-EventQueue-0] INFO  com.ticketbooking.dao.BookingDAO - Database connection closed after booking operation
2025-05-20 17:45:02.487 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Booking created for event ID: 2 by customer: ABDOUALYE
2025-05-20 17:45:31.381 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 17:45:31.397 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 17:45:31.413 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 18:41:44.757 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: Abubakar
2025-05-20 18:41:44.818 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:41:44.836 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if email exists: <EMAIL>
2025-05-20 18:41:44.847 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:41:44.857 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Registering new user: Abubakar
2025-05-20 18:41:44.867 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:41:44.882 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:41:44.892 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error adding user: Abubakar
org.postgresql.util.PSQLException: ERROR: column "full_name" of relation "users" does not exist
  Position: 53
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.addUser(UserDAO.java:177) ~[build/:?]
	at com.ticketbooking.controller.UserController.registerUser(UserController.java:168) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel.handleRegistration(RegistrationPanel.java:453) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel$3.actionPerformed(RegistrationPanel.java:135) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 18:41:44.920 [AWT-EventQueue-0] ERROR com.ticketbooking.view.RegistrationPanel - Registration failed for username: Abubakar
2025-05-20 18:42:26.640 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Authenticating user: admin
2025-05-20 18:42:26.651 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:42:26.656 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error updating last login for user ID: 1
org.postgresql.util.PSQLException: ERROR: column "last_login_date" of relation "users" does not exist
  Position: 18
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.updateLastLogin(UserDAO.java:389) ~[build/:?]
	at com.ticketbooking.dao.UserDAO.authenticateUser(UserDAO.java:141) ~[build/:?]
	at com.ticketbooking.controller.UserController.authenticateUser(UserController.java:78) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.handleAdminLogin(LoginPanel.java:411) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.lambda$createLoginPanel$1(LoginPanel.java:242) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 18:42:26.658 [AWT-EventQueue-0] INFO  com.ticketbooking.dao.UserDAO - User authenticated successfully: admin
2025-05-20 18:42:26.658 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Admin logged in: admin
2025-05-20 18:42:26.662 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load menu icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.updateMenuBar(MainFrame.java:161) ~[build/:?]
	at com.ticketbooking.view.MainFrame.setCurrentUser(MainFrame.java:135) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.handleAdminLogin(LoginPanel.java:415) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.lambda$createLoginPanel$1(LoginPanel.java:242) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 18:42:26.675 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 18:42:26.686 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:42:26.692 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 18:42:26.693 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 18:42:26.702 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:42:26.715 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:42:26.726 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:42:26.729 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Booking list refreshed
2025-05-20 18:42:26.730 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 18:42:26.739 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:42:26.742 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - User list refreshed
2025-05-20 18:42:26.742 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 18:42:26.751 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:42:26.759 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 18:42:26.767 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:42:26.781 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:42:26.791 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:42:26.795 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 18:42:26.804 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:22.882 [main] INFO  com.ticketbooking.Main - Starting Ticket Booking System
2025-05-20 18:58:23.882 [main] INFO  com.ticketbooking.Main - Set Nimbus look and feel with custom colors
2025-05-20 18:58:23.947 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load application icon
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:55) ~[build/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[build/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 18:58:23.983 [AWT-EventQueue-0] WARN  com.ticketbooking.view.LoginPanel - Could not load logo image
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.LoginPanel.initializeComponents(LoginPanel.java:78) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.<init>(LoginPanel.java:57) ~[build/:?]
	at com.ticketbooking.view.MainFrame.initializePanels(MainFrame.java:105) ~[build/:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:69) ~[build/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[build/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 18:58:27.216 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Login panel initialized
2025-05-20 18:58:27.360 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 18:58:27.644 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:27.902 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 18:58:27.903 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Home panel initialized
2025-05-20 18:58:28.066 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Booking panel initialized
2025-05-20 18:58:28.425 [AWT-EventQueue-0] WARN  com.ticketbooking.view.AdminPanel - Could not load tab icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.AdminPanel.initializeComponents(AdminPanel.java:162) ~[build/:?]
	at com.ticketbooking.view.AdminPanel.<init>(AdminPanel.java:67) ~[build/:?]
	at com.ticketbooking.view.MainFrame.initializePanels(MainFrame.java:108) ~[build/:?]
	at com.ticketbooking.view.MainFrame.<init>(MainFrame.java:69) ~[build/:?]
	at com.ticketbooking.Main.lambda$main$0(Main.java:59) ~[build/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 18:58:28.428 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 18:58:28.448 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:28.454 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 18:58:28.454 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 18:58:28.466 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:28.497 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:28.514 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:28.522 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Booking list refreshed
2025-05-20 18:58:28.522 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 18:58:28.535 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:28.551 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - User list refreshed
2025-05-20 18:58:28.551 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 18:58:28.563 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:28.569 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 18:58:28.582 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:28.601 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:28.619 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:28.623 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 18:58:28.635 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:28.640 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Admin panel initialized
2025-05-20 18:58:28.676 [AWT-EventQueue-0] INFO  com.ticketbooking.view.RegistrationPanel - Registration panel initialized
2025-05-20 18:58:28.762 [AWT-EventQueue-0] INFO  com.ticketbooking.view.MainFrame - Main frame initialized
2025-05-20 18:58:31.719 [AWT-EventQueue-0] INFO  com.ticketbooking.Main - Application started successfully
2025-05-20 18:58:38.301 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Guest login
2025-05-20 18:58:38.314 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load menu icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.updateMenuBar(MainFrame.java:161) ~[build/:?]
	at com.ticketbooking.view.MainFrame.setCurrentUser(MainFrame.java:135) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.handleGuestLogin(LoginPanel.java:438) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.lambda$createLoginPanel$4(LoginPanel.java:258) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 18:58:38.348 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 18:58:38.363 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:38.389 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 18:58:40.934 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting event with ID: 1
2025-05-20 18:58:40.950 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:40.965 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Loaded event details for event ID: 1
2025-05-20 18:58:45.607 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 18:58:45.627 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:58:45.659 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 18:59:26.709 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting event with ID: 2
2025-05-20 18:59:26.724 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:26.729 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Loaded event details for event ID: 2
2025-05-20 18:59:29.210 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 18:59:29.223 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:29.239 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 18:59:44.782 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Authenticating user: admin
2025-05-20 18:59:44.795 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:44.808 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error updating last login for user ID: 1
org.postgresql.util.PSQLException: ERROR: column "last_login_date" of relation "users" does not exist
  Position: 18
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.updateLastLogin(UserDAO.java:389) ~[build/:?]
	at com.ticketbooking.dao.UserDAO.authenticateUser(UserDAO.java:141) ~[build/:?]
	at com.ticketbooking.controller.UserController.authenticateUser(UserController.java:82) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.handleAdminLogin(LoginPanel.java:411) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.lambda$createLoginPanel$0(LoginPanel.java:214) ~[build/:?]
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:525) ~[?:?]
	at javax.swing.JTextField.postActionEvent(JTextField.java:740) ~[?:?]
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:856) ~[?:?]
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1810) ~[?:?]
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2956) ~[?:?]
	at javax.swing.JComponent.processKeyBindings(JComponent.java:3004) ~[?:?]
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2918) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6398) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1942) ~[?:?]
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:883) ~[?:?]
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1146) ~[?:?]
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:1020) ~[?:?]
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:848) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4877) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 18:59:44.811 [AWT-EventQueue-0] INFO  com.ticketbooking.dao.UserDAO - User authenticated successfully: admin
2025-05-20 18:59:44.811 [AWT-EventQueue-0] INFO  com.ticketbooking.view.LoginPanel - Admin logged in: admin
2025-05-20 18:59:44.812 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load menu icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.updateMenuBar(MainFrame.java:161) ~[build/:?]
	at com.ticketbooking.view.MainFrame.setCurrentUser(MainFrame.java:135) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.handleAdminLogin(LoginPanel.java:415) ~[build/:?]
	at com.ticketbooking.view.LoginPanel.lambda$createLoginPanel$0(LoginPanel.java:214) ~[build/:?]
	at javax.swing.JTextField.fireActionPerformed(JTextField.java:525) ~[?:?]
	at javax.swing.JTextField.postActionEvent(JTextField.java:740) ~[?:?]
	at javax.swing.JTextField$NotifyAction.actionPerformed(JTextField.java:856) ~[?:?]
	at javax.swing.SwingUtilities.notifyAction(SwingUtilities.java:1810) ~[?:?]
	at javax.swing.JComponent.processKeyBinding(JComponent.java:2956) ~[?:?]
	at javax.swing.JComponent.processKeyBindings(JComponent.java:3004) ~[?:?]
	at javax.swing.JComponent.processKeyEvent(JComponent.java:2918) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6398) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.KeyboardFocusManager.redispatchEvent(KeyboardFocusManager.java:1942) ~[?:?]
	at java.awt.DefaultKeyboardFocusManager.dispatchKeyEvent(DefaultKeyboardFocusManager.java:883) ~[?:?]
	at java.awt.DefaultKeyboardFocusManager.preDispatchKeyEvent(DefaultKeyboardFocusManager.java:1146) ~[?:?]
	at java.awt.DefaultKeyboardFocusManager.typeAheadAssertions(DefaultKeyboardFocusManager.java:1020) ~[?:?]
	at java.awt.DefaultKeyboardFocusManager.dispatchEvent(DefaultKeyboardFocusManager.java:848) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4877) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 18:59:44.825 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 18:59:44.838 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:44.844 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Event list refreshed
2025-05-20 18:59:44.845 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 18:59:44.858 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:44.878 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:44.895 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:44.900 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - Booking list refreshed
2025-05-20 18:59:44.901 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 18:59:44.916 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:44.921 [AWT-EventQueue-0] INFO  com.ticketbooking.view.AdminPanel - User list refreshed
2025-05-20 18:59:44.922 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 18:59:44.935 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:44.941 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Getting all bookings
2025-05-20 18:59:44.953 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:44.971 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:44.990 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 18:59:44.995 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Getting all users
2025-05-20 18:59:45.007 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:00:19.321 [AWT-EventQueue-0] WARN  com.ticketbooking.view.MainFrame - Could not load menu icons
java.lang.NullPointerException: Cannot invoke "java.net.URL.toExternalForm()" because "location" is null
	at javax.swing.ImageIcon.<init>(ImageIcon.java:232) ~[?:?]
	at com.ticketbooking.view.MainFrame.updateMenuBar(MainFrame.java:161) ~[build/:?]
	at com.ticketbooking.view.MainFrame.lambda$updateMenuBar$3(MainFrame.java:222) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.AbstractButton.doClick(AbstractButton.java:374) ~[?:?]
	at javax.swing.plaf.basic.BasicMenuItemUI.doClick(BasicMenuItemUI.java:1029) ~[?:?]
	at javax.swing.plaf.basic.BasicMenuItemUI$Handler.mouseReleased(BasicMenuItemUI.java:1073) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 19:00:32.818 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: user
2025-05-20 19:00:32.834 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:00:45.869 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if email exists: <EMAIL>
2025-05-20 19:00:45.883 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:00:57.070 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: user
2025-05-20 19:00:57.086 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:02.836 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: user
2025-05-20 19:01:02.852 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:21.438 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: user
2025-05-20 19:01:21.450 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:21.455 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if email exists: <EMAIL>
2025-05-20 19:01:21.466 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:21.471 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Registering new user: user
2025-05-20 19:01:21.482 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:21.497 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:21.513 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:21.515 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error adding user: user
org.postgresql.util.PSQLException: ERROR: column "full_name" of relation "users" does not exist
  Position: 53
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.addUser(UserDAO.java:177) ~[build/:?]
	at com.ticketbooking.controller.UserController.registerUser(UserController.java:179) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel.handleRegistration(RegistrationPanel.java:453) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel$3.actionPerformed(RegistrationPanel.java:135) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 19:01:21.517 [AWT-EventQueue-0] ERROR com.ticketbooking.view.RegistrationPanel - Registration failed for username: user
2025-05-20 19:01:26.738 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: user
2025-05-20 19:01:26.750 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:26.755 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if email exists: <EMAIL>
2025-05-20 19:01:26.766 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:26.771 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Registering new user: user
2025-05-20 19:01:26.782 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:26.797 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:26.816 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:01:26.818 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error adding user: user
org.postgresql.util.PSQLException: ERROR: column "full_name" of relation "users" does not exist
  Position: 53
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.addUser(UserDAO.java:177) ~[build/:?]
	at com.ticketbooking.controller.UserController.registerUser(UserController.java:179) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel.handleRegistration(RegistrationPanel.java:453) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel$3.actionPerformed(RegistrationPanel.java:135) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 19:01:26.822 [AWT-EventQueue-0] ERROR com.ticketbooking.view.RegistrationPanel - Registration failed for username: user
2025-05-20 19:03:50.131 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: user7
2025-05-20 19:03:50.146 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:03:50.293 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if username exists: user7
2025-05-20 19:03:50.306 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:03:50.313 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Checking if email exists: <EMAIL>
2025-05-20 19:03:50.329 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:03:50.334 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.UserController - Registering new user: user7
2025-05-20 19:03:50.346 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:03:50.363 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:03:50.381 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:03:50.383 [AWT-EventQueue-0] ERROR com.ticketbooking.dao.UserDAO - Error adding user: user7
org.postgresql.util.PSQLException: ERROR: column "full_name" of relation "users" does not exist
  Position: 53
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190) ~[postgresql-42.6.0.jar:42.6.0]
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152) ~[postgresql-42.6.0.jar:42.6.0]
	at com.ticketbooking.dao.UserDAO.addUser(UserDAO.java:177) ~[build/:?]
	at com.ticketbooking.controller.UserController.registerUser(UserController.java:179) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel.handleRegistration(RegistrationPanel.java:453) ~[build/:?]
	at com.ticketbooking.view.RegistrationPanel$3.actionPerformed(RegistrationPanel.java:135) ~[build/:?]
	at javax.swing.AbstractButton.fireActionPerformed(AbstractButton.java:1972) ~[?:?]
	at javax.swing.AbstractButton$Handler.actionPerformed(AbstractButton.java:2314) ~[?:?]
	at javax.swing.DefaultButtonModel.fireActionPerformed(DefaultButtonModel.java:407) ~[?:?]
	at javax.swing.DefaultButtonModel.setPressed(DefaultButtonModel.java:262) ~[?:?]
	at javax.swing.plaf.basic.BasicButtonListener.mouseReleased(BasicButtonListener.java:279) ~[?:?]
	at java.awt.Component.processMouseEvent(Component.java:6621) ~[?:?]
	at javax.swing.JComponent.processMouseEvent(JComponent.java:3398) ~[?:?]
	at java.awt.Component.processEvent(Component.java:6386) ~[?:?]
	at java.awt.Container.processEvent(Container.java:2266) ~[?:?]
	at java.awt.Component.dispatchEventImpl(Component.java:4996) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2324) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4948) ~[?:?]
	at java.awt.LightweightDispatcher.processMouseEvent(Container.java:4575) ~[?:?]
	at java.awt.LightweightDispatcher.dispatchEvent(Container.java:4516) ~[?:?]
	at java.awt.Container.dispatchEventImpl(Container.java:2310) ~[?:?]
	at java.awt.Window.dispatchEventImpl(Window.java:2780) ~[?:?]
	at java.awt.Component.dispatchEvent(Component.java:4828) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:775) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:747) ~[?:?]
	at java.awt.EventQueue$5.run(EventQueue.java:745) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) [?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) [?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:744) [?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) [?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) [?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) [?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) [?:?]
2025-05-20 19:03:50.385 [AWT-EventQueue-0] ERROR com.ticketbooking.view.RegistrationPanel - Registration failed for username: user7
2025-05-20 19:04:34.542 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 19:04:34.558 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:04:34.578 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 19:04:40.215 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 19:04:40.230 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:04:40.247 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 19:04:40.247 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 19:04:40.261 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:04:40.280 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 19:04:41.548 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 19:04:41.562 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:04:41.579 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 19:04:41.580 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 19:04:41.593 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:04:41.611 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 19:04:42.581 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 19:04:42.595 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:04:42.611 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 19:04:42.612 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 19:04:42.625 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:04:42.643 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
2025-05-20 19:05:36.437 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting event with ID: 2
2025-05-20 19:05:36.450 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:05:36.454 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Loaded event details for event ID: 2
2025-05-20 19:06:06.841 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.BookingController - Creating new booking for event ID: 2 by customer: hj
2025-05-20 19:06:06.841 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting event with ID: 2
2025-05-20 19:06:06.854 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:06:06.869 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:06:06.869 [AWT-EventQueue-0] INFO  com.ticketbooking.dao.BookingDAO - Starting transaction for new booking by: hj
2025-05-20 19:06:06.883 [AWT-EventQueue-0] INFO  com.ticketbooking.dao.BookingDAO - Updated available seats for event ID: 2, reduced by: 4
2025-05-20 19:06:06.890 [AWT-EventQueue-0] INFO  com.ticketbooking.dao.BookingDAO - Booking transaction committed successfully. Booking ID: 4
2025-05-20 19:06:06.891 [AWT-EventQueue-0] INFO  com.ticketbooking.dao.BookingDAO - Database connection closed after booking operation
2025-05-20 19:06:06.891 [AWT-EventQueue-0] INFO  com.ticketbooking.view.BookingPanel - Booking created for event ID: 2 by customer: hj
2025-05-20 19:06:21.095 [AWT-EventQueue-0] INFO  com.ticketbooking.controller.EventController - Getting all events
2025-05-20 19:06:21.109 [AWT-EventQueue-0] INFO  com.ticketbooking.database.DBConnection - Database connection established
2025-05-20 19:06:21.125 [AWT-EventQueue-0] INFO  com.ticketbooking.view.HomePanel - Event list refreshed with filter: All Events, found 3 events
